{"name": "claude-flow", "version": "2.0.0", "description": "Enterprise-grade AI agent orchestration with ruv-swarm integration", "main": "cli.js", "bin": {"claude-flow": "./cli.js"}, "scripts": {"dev": "tsx src/cli/main.ts", "build": "npm run build:ts && npm run build:binary", "build:ts": "tsc", "build:binary": "pkg dist/cli/main.js --targets node20-linux-x64,node20-macos-x64,node20-win-x64 --output bin/claude-flow", "build:deno": "PATH=\"/home/<USER>/.deno/bin:$PATH\" deno compile --allow-all --no-check --output=bin/claude-flow-deno src/cli/main.ts", "build:simple": "npm run build:ts && pkg dist/cli/simple-cli.js --output bin/claude-flow-simple", "typecheck": "tsc --noEmit", "test": "jest", "test:deno": "deno task test", "lint": "eslint src --ext .ts,.js", "format": "prettier --write src", "postinstall": "node scripts/install.js", "prepublishOnly": "npm run build && npm run test && npm run lint", "publish:major": "npm version major && npm publish", "publish:minor": "npm version minor && npm publish", "publish:patch": "npm version patch && npm publish", "prepack": "npm run build", "postpack": "echo 'Package created successfully'", "prepare-publish": "node scripts/prepare-publish.js"}, "keywords": ["claude", "ai", "agent", "orchestration", "mcp", "workflow", "automation", "swarm", "ruv-swarm", "github", "docker", "enterprise", "coordination", "multi-agent", "neural-networks", "cli", "tools"], "author": "rUv", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ruvnet/claude-code-flow.git"}, "bugs": {"url": "https://github.com/ruvnet/claude-code-flow/issues"}, "homepage": "https://github.com/ruvnet/claude-code-flow#readme", "engines": {"node": ">=20.0.0", "npm": ">=9.0.0"}, "files": ["cli.js", "bin/", "dist/", "src/", ".claude/", "docker-test/", "scripts/", "README.md", "LICENSE", "CHANGELOG.md", "DOCKER_TEST_REPORT.md", "deno.json"], "dependencies": {"@types/better-sqlite3": "^7.6.13", "better-sqlite3": "^12.2.0", "blessed": "^0.1.81", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "commander": "^11.1.0", "cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "inquirer": "^9.2.12", "nanoid": "^5.0.4", "node-pty": "^1.0.0", "ora": "^7.0.1", "ruv-swarm": "file:../../ruv-swarm/npm", "ws": "^8.14.2"}, "devDependencies": {"@swc/cli": "^0.1.63", "@swc/core": "^1.3.101", "@types/blessed": "^0.1.25", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.8", "@types/node": "^20.10.5", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "pkg": "^5.8.1", "prettier": "^3.1.1", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "pkg": {"targets": ["node20-linux-x64", "node20-macos-x64", "node20-win-x64"], "scripts": "dist/**/*.js", "outputPath": "bin", "options": ["--experimental-specifier-resolution=node"]}, "type": "module", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}