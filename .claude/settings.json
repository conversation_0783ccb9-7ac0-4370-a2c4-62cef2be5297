{"env": {"RUV_SWARM_AUTO_COMMIT": "false", "RUV_SWARM_AUTO_PUSH": "false", "RUV_SWARM_HOOKS_ENABLED": "false", "RUV_SWARM_TELEMETRY_ENABLED": "true", "RUV_SWARM_REMOTE_EXECUTION": "true"}, "permissions": {"allow": ["<PERSON>sh(npx ruv-swarm *)", "Bash(npm run lint)", "Bash(npm run test:*)", "Bash(npm test *)", "Bash(git status)", "<PERSON><PERSON>(git diff *)", "Bash(git log *)", "Bash(git add *)", "<PERSON><PERSON>(git commit *)", "<PERSON><PERSON>(git push)", "<PERSON>sh(git config *)", "<PERSON><PERSON>(node *)", "Ba<PERSON>(which *)", "Bash(pwd)", "Bash(ls *)"], "deny": ["Bash(rm -rf /)", "Bash(curl * | bash)", "Bash(wget * | sh)", "<PERSON><PERSON>(eval *)"]}, "hooks": {}, "mcpServers": {"ruv-swarm": {"command": "npx", "args": ["ruv-swarm", "mcp", "start"], "env": {"RUV_SWARM_HOOKS_ENABLED": "false", "RUV_SWARM_TELEMETRY_ENABLED": "true", "RUV_SWARM_REMOTE_READY": "true"}}}, "includeCoAuthoredBy": true}