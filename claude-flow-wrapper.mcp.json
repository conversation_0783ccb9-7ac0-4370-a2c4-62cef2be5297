{"name": "claude-flow-wrapper", "version": "2.0.0", "description": "Claude-Flow MCP Wrapper with SPARC methodology integration", "tools": {"sparc_orchestrator": {"description": "Multi-agent coordination and task orchestration", "passThrough": "Task", "promptInjection": true, "category": "coordination"}, "sparc_coder": {"description": "Autonomous code generation and implementation", "passThrough": "Task", "promptInjection": true, "category": "development"}, "sparc_researcher": {"description": "Deep research and information gathering", "passThrough": "Task", "promptInjection": true, "category": "research"}, "sparc_tdd": {"description": "Test-driven development workflow", "passThrough": "Task", "promptInjection": true, "category": "testing"}, "sparc_architect": {"description": "System design and architecture planning", "passThrough": "Task", "promptInjection": true, "category": "architecture"}, "sparc_reviewer": {"description": "Code review and quality assurance", "passThrough": "Task", "promptInjection": true, "category": "quality"}, "sparc_debugger": {"description": "Debugging and issue resolution", "passThrough": "Task", "promptInjection": true, "category": "debugging"}, "sparc_tester": {"description": "Testing and validation", "passThrough": "Task", "promptInjection": true, "category": "testing"}, "sparc_analyzer": {"description": "Code and system analysis", "passThrough": "Task", "promptInjection": true, "category": "analysis"}, "sparc_optimizer": {"description": "Performance optimization", "passThrough": "Task", "promptInjection": true, "category": "optimization"}, "sparc_documenter": {"description": "Documentation generation and maintenance", "passThrough": "Task", "promptInjection": true, "category": "documentation"}, "sparc_designer": {"description": "UI/UX design and user experience", "passThrough": "Task", "promptInjection": true, "category": "design"}, "sparc_innovator": {"description": "Creative solutions and innovation", "passThrough": "Task", "promptInjection": true, "category": "innovation"}, "sparc_swarm-coordinator": {"description": "Swarm management and coordination", "passThrough": "Task", "promptInjection": true, "category": "swarm"}, "sparc_memory-manager": {"description": "Memory and knowledge management", "passThrough": "Task", "promptInjection": true, "category": "memory"}, "sparc_batch-executor": {"description": "Parallel execution and batch processing", "passThrough": "Task", "promptInjection": true, "category": "execution"}, "sparc_workflow-manager": {"description": "Workflow automation and management", "passThrough": "Task", "promptInjection": true, "category": "workflow"}}, "meta_tools": {"sparc_list": {"description": "List all available SPARC modes", "category": "meta"}, "sparc_swarm": {"description": "Coordinate multiple agents in swarm mode", "category": "swarm"}, "sparc_swarm_status": {"description": "Check swarm coordination status", "category": "swarm"}}, "features": {"promptInjection": true, "sparcMethodology": true, "swarmCoordination": true, "memoryIntegration": true, "parallelExecution": true, "workflowAutomation": true}, "configuration": {"defaultMode": "sparc_coder", "enableLogging": true, "logLevel": "info", "maxConcurrentTasks": 5, "memoryPersistence": true, "swarmEnabled": true}}