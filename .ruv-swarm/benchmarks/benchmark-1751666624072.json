{"metadata": {"timestamp": "2025-07-04T22:03:43.877Z", "version": "0.2.0", "testType": "comprehensive", "iterations": 5, "system": {"platform": "linux", "arch": "x64", "nodeVersion": "v20.19.0"}}, "benchmarks": {"wasmLoading": {"time": 51, "target": 100, "status": "PASS"}, "swarmInit": {"times": [5, 5, 5, 6, 5], "average": 5.2, "min": 5, "max": 6, "target": 10, "status": "PASS"}, "agentSpawn": {"times": [3, 4, 4, 3, 3], "average": 3.4, "target": 5, "status": "PASS"}, "neuralProcessing": {"times": [20, 20, 20, 21, 20], "average": 20.2, "throughput": 49.504950495049506, "target": 50, "status": "PASS"}, "memory": {"heapUsed": 8611456, "heapTotal": 12115968, "external": 21650730, "rss": 62980096, "efficiency": "71.1"}}, "overallScore": 80}