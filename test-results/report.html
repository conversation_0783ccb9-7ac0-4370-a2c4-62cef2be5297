<!DOCTYPE html>
<html>
<head>
    <title>Claude-Flow Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: flex; gap: 20px; margin-bottom: 20px; }
        .metric { background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd; text-align: center; }
        .metric h3 { margin: 0; color: #333; }
        .metric .value { font-size: 24px; font-weight: bold; margin: 10px 0; }
        .passed .value { color: #28a745; }
        .failed .value { color: #dc3545; }
        .total .value { color: #007bff; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .passed { background-color: #d4edda; }
        .failed { background-color: #f8d7da; }
        .footer { margin-top: 30px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Claude-Flow Test Report</h1>
        <p>Generated on: 2025-06-16T21:54:16.792Z</p>
    </div>
    
    <div class="summary">
        <div class="metric total">
            <h3>Total Tests</h3>
            <div class="value">3</div>
        </div>
        <div class="metric passed">
            <h3>Passed</h3>
            <div class="value">0</div>
        </div>
        <div class="metric failed">
            <h3>Failed</h3>
            <div class="value">3</div>
        </div>
        <div class="metric total">
            <h3>Duration</h3>
            <div class="value">5905ms</div>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Test Suite</th>
                <th>Status</th>
                <th>Duration</th>
            </tr>
        </thead>
        <tbody>
            
        <tr class="failed">
          <td>unit</td>
          <td>❌ FAILED</td>
          <td>2616ms</td>
        </tr>
        <tr class="failed">
          <td>integration</td>
          <td>❌ FAILED</td>
          <td>2262ms</td>
        </tr>
        <tr class="failed">
          <td>e2e</td>
          <td>❌ FAILED</td>
          <td>1027ms</td>
        </tr>
        </tbody>
    </table>

    <div class="footer">
        <p>Claude-Flow Test Suite - Advanced AI Agent Orchestration System</p>
    </div>
</body>
</html>