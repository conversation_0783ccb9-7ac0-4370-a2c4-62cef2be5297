[0m[33mWarning[0m The following peer dependency issues were found:
[0m[38;5;245m├─┬[0m jest@29.7.0
[0m[38;5;245m│ └─┬[0m jest-cli@29.7.0
[0m[38;5;245m│   └─┬[0m jest-config@29.7.0
[0m[38;5;245m│     └──[0m peer @types/node: resolved to 20.19.0
[0m[38;5;245m├─┬[0m jest@29.7.0
[0m[38;5;245m│ └─┬[0m jest-cli@29.7.0
[0m[38;5;245m│   └─┬[0m create-jest@29.7.0
[0m[38;5;245m│     └─┬[0m jest-config@29.7.0
[0m[38;5;245m│       └──[0m peer @types/node: resolved to 20.19.0
[0m[38;5;245m├─┬[0m ts-jest@29.4.0
[0m[38;5;245m│ └─┬[0m jest@29.7.0
[0m[38;5;245m│   └─┬[0m jest-cli@29.7.0
[0m[38;5;245m│     └─┬[0m jest-config@29.7.0
[0m[38;5;245m│       └──[0m peer @types/node: resolved to 20.19.0
[0m[38;5;245m└─┬[0m ts-jest@29.4.0
[0m[38;5;245m  └─┬[0m jest@29.7.0
[0m[38;5;245m    └─┬[0m jest-cli@29.7.0
[0m[38;5;245m      └─┬[0m create-jest@29.7.0
[0m[38;5;245m        └─┬[0m jest-config@29.7.0
[0m[38;5;245m          └──[0m peer @types/node: resolved to 20.19.0

[0m[32mDownload[0m https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.10.0.tgz
[0m[32mInitialize[0m better-sqlite3@11.10.0
[0m[33mWarning[0m The following packages contained npm lifecycle scripts ([0m[38;5;245mpreinstall/install/postinstall[0m) that were not executed:
┠─ [0m[38;5;245mnpm:better-sqlite3@11.10.0[0m
┃
┠─ [0m[3mThis may cause the packages to not work correctly.[0m
┖─ [0m[3mTo run lifecycle scripts, use the `--allow-scripts` flag with `deno install`:[0m
   [0m[1mdeno install --allow-scripts=npm:better-sqlite3@11.10.0[0m
[0m[1m[31merror[0m: Relative import path "fs" not prefixed with / or ./ or ../ and not in import map from "file:///workspaces/claude-code-flow/src/cli/simple-commands/init/rollback/backup-manager.js"
  [0m[36mhint:[0m If you want to use a built-in Node module, add a "node:" prefix (ex. "node:fs").
    at [0m[36mfile:///workspaces/claude-code-flow/src/cli/simple-commands/init/rollback/backup-manager.js[0m:[0m[33m4[0m:[0m[33m16[0m
